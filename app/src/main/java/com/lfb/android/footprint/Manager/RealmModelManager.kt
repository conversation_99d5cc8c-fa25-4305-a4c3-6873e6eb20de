package com.lfb.android.footprint.Manager

import android.content.Context
import com.mapbox.geojson.Point
import io.realm.kotlin.Realm
import io.realm.kotlin.RealmConfiguration
import io.realm.kotlin.ext.query
import io.realm.kotlin.types.RealmObject
import com.lfb.android.footprint.model.StepDataRealmModel
import com.lfb.android.footprint.prefs.AppPrefs
import io.realm.kotlin.query.Sort
import kotlinx.datetime.*
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class RealmModelManager private constructor(private val context: Context? = null) {

    // 初始化 Realm 实例
    val realm: Realm = initializeRealm()

    // 使用通用缓存管理器（支持磁盘缓存）
    private val cacheManager = DataCacheManager.getInstance(context)

    // Realm 初始化方法
    private fun initializeRealm(): Realm {
        val config = RealmConfiguration.Builder(schema = setOf(StepDataRealmModel::class))
            .deleteRealmIfMigrationNeeded()
            .build()
        // 获取数据库文件路径
        val realmFilePath = config.path
        println("Realm数据库文件路径：$realmFilePath")
        return Realm.open(config)
    }

    companion object {
        @Volatile
        private var instance: RealmModelManager? = null

        fun getInstance(context: Context? = null): RealmModelManager {
            return instance ?: synchronized(this) {
                instance ?: RealmModelManager(context).also { instance = it }
            }
        }
    }

    // 通用的写入方法
    suspend inline fun <reified T : RealmObject> writeToRealm(
        crossinline block: StepDataRealmModel,
        function: () -> Unit
    ) {
        realm.write {
            copyToRealm(T::class.java.getDeclaredConstructor().newInstance().apply(block))
        }
    }

    // 批量写入方法（优化版本）
    suspend fun writeBatchToRealm(models: List<StepDataRealmModel>) {
        if (models.isEmpty()) return

        realm.write {
            models.forEach { model ->
                try {
                    copyToRealm(model)  // 尝试写入数据
                } catch (e: Exception) {
                    // 记录错误，或根据需要进行其他处理
//                    println("Failed to write model: ${model.dataTime}, Error: ${e.message}")
                }
            }
        }
    }

    // 高性能批量写入方法（适用于大数据量导入）
    suspend fun writeBatchToRealmOptimized(models: List<StepDataRealmModel>) {
        if (models.isEmpty()) return

        try {
            realm.write {
                // 使用单个事务写入所有数据，提高性能
                models.forEach { model ->
                    try {
                        copyToRealm(model)
                    } catch (e: Exception) {
                        // 记录错误但继续处理其他数据
//                        println("Failed to write model: ${model.dataTime}, Error: ${e.message}")
                    }
                }
            }
            // 写入成功后清除所有缓存
            clearAllCaches()
        } catch (e: Exception) {
            println("Batch write failed: ${e.message}")
            throw e
        }
    }

    // 示例：查询指定时间范围的步数数据
    suspend fun getStepDataByTimeRange(startTime: Long, endTime: Long): List<StepDataRealmModel> {
        return realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime <= $1", startTime, endTime).find()
    }

    // 按天分组获取指定时间范围的轨迹数据
    suspend fun getStepDataByTimeRangeGroupedByDay(startTime: Long, endTime: Long): Map<String, List<StepDataRealmModel>> {
        val allData = realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime <= $1", startTime, endTime)
            .sort("dataTime", Sort.ASCENDING)
            .find()

        return allData.groupBy { stepData ->
            val dateTime = Instant.fromEpochSeconds(stepData.dataTime)
                .toLocalDateTime(TimeZone.currentSystemDefault())
            "${dateTime.year}-${dateTime.monthNumber.toString().padStart(2, '0')}-${dateTime.dayOfMonth.toString().padStart(2, '0')}"
        }
    }

    // 按天分组获取七日轨迹数据
    suspend fun getWeekDataGroupedByDay(): Map<String, List<StepDataRealmModel>> {
        val (start, end) = getWeekRange()
        return getStepDataByTimeRangeGroupedByDay(start, end)
    }

    // 查询指定时间
    suspend inline fun <reified T : StepDataRealmModel> queryModelByTime(dataTime: Long): StepDataRealmModel? {
        return realm.query<T>("dataTime == $0", dataTime).first().find()
    }

    // 查询某天的数据
    suspend inline fun <reified T : StepDataRealmModel> queryOneDayData(dayStart: Long, dayEnd: Long): List<StepDataRealmModel> {
        return realm.query<T>("dataTime >= $0 && dataTime < $1", dayStart, dayEnd).find()
    }

    // 查询区间数据
    suspend inline fun <reified T : StepDataRealmModel> queryDataBetween(startTime: Long, endTime: Long): List<StepDataRealmModel> {
        return realm.query<T>("dataTime >= $0 && dataTime <= $1", startTime, endTime).find()
    }

    // 查询所有
    suspend inline fun <reified T : StepDataRealmModel> queryAllData(): List<StepDataRealmModel> {
        return realm.query<T>().find()
    }

    // 查询所有（升序/降序）
    suspend inline fun <reified T : StepDataRealmModel> queryAllDataWithAscending(ascending: Boolean = true): List<T> {
        val sortOrder = if (ascending) Sort.ASCENDING else Sort.DESCENDING
        return realm.query<T>().sort("dataTime", sortOrder).find()
    }

    // 查询今天的数据
    suspend inline fun <reified T : StepDataRealmModel> queryTodayData(): List<StepDataRealmModel> {
        val (start, end) = getTodayRange()
        return queryOneDayData<T>(start, end)
    }

    // 查询昨天的数据
    suspend inline fun <reified T : StepDataRealmModel> queryYesterdayData(): List<StepDataRealmModel> {
        val (start, end) = getYesterdayRange()
        return queryOneDayData<T>(start, end)
    }

    // 查询本周数据
    suspend inline fun <reified T : StepDataRealmModel> queryWeekAllData(): List<StepDataRealmModel> {
        val (start, end) = getWeekRange()
        return queryDataBetween<T>(start, end)
    }

    // 查询本月数据
    suspend inline fun <reified T : StepDataRealmModel> queryMonthData(): List<StepDataRealmModel> {
        val (start, end) = getMonthRange()
        return queryDataBetween<T>(start, end)
    }

    // 查询本年数据
    suspend inline fun <reified T : StepDataRealmModel> queryYearData(): List<StepDataRealmModel> {
        val (start, end) = getYearRange()
        return queryDataBetween<T>(start, end)
    }

    // ====== 时间区间计算 ======
    fun getTodayRange(): Pair<Long, Long> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val start = now.atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val end = (now.plus(DatePeriod(days = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        return start to end
    }

    fun getYesterdayRange(): Pair<Long, Long> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val start = (now.minus(DatePeriod(days = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val end = now.atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        return start to end
    }

    fun getWeekRange(): Pair<Long, Long> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val end = (now.plus(DatePeriod(days = 1))).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val start = (end - 7 * 24 * 60 * 60)
        return start to end
    }

    fun getMonthRange(): Pair<Long, Long> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val start = LocalDate(now.year, now.month, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val end = (if (now.monthNumber == 1) LocalDate(now.year - 1, 12, 1) else LocalDate(now.year, now.monthNumber - 1, 1)).atStartOfDayIn(
            TimeZone.currentSystemDefault()).epochSeconds
        return start to end
    }

    fun getYearRange(): Pair<Long, Long> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val start = LocalDate(now.year-1, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val end = LocalDate(now.year, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        return start to end
    }

    // ====== 详细数据面板查询方法 ======

    // 获取所有有数据的年份 - 高性能优化版本（带缓存）
    suspend fun getAvailableYears(): List<Int> = withContext(Dispatchers.IO) {
        cacheManager.getOrCompute(
            key = DataCacheManager.Companion.CacheKeys.AVAILABLE_YEARS,
            ttl = DataCacheManager.Companion.CacheTTL.MEDIUM
        ) {
            getAvailableYearsFromDatabase()
        }
    }

    // 从数据库获取年份数据的核心方法
    private suspend fun getAvailableYearsFromDatabase(): List<Int> {
        // 方法1：使用 distinct 查询（最高效）
        // 利用新添加的年份索引，直接在数据库层面去重
        try {
            val distinctYears = realm.query<StepDataRealmModel>()
                .distinct("year")
                .sort("year", Sort.DESCENDING)
                .find()
                .map { it.year }
                .distinct() // 确保去重
                .sortedDescending() // 最新年份在前

            println("getAvailableYears: distinct查询结果: $distinctYears")

            if (distinctYears.isNotEmpty()) {
                return distinctYears
            }
        } catch (e: Exception) {
            println("Distinct query failed, falling back to batch query: ${e.message}")
        }

        // 方法2：如果 distinct 查询失败，使用优化的分批查询
        val years = mutableSetOf<Int>()
        val batchSize = 3000 // 减小批次大小，提高响应速度
        var consecutiveSameYearCount = 0
        var lastProcessedYear: Int? = null
        var lastDataTime = Long.MAX_VALUE // 用于基于时间戳的分页，从最新数据开始

        while (true) {
            // 使用基于时间戳的分页，比offset更高效且正确
            val batch = if (lastDataTime == Long.MAX_VALUE) {
                // 第一批数据，从最新的时间开始
                realm.query<StepDataRealmModel>()
                    .sort("dataTime", Sort.DESCENDING)
                    .limit(batchSize)
                    .find()
            } else {
                // 后续批次，查询时间戳小于上一批最后一个数据的时间戳
                realm.query<StepDataRealmModel>("dataTime < $0", lastDataTime)
                    .sort("dataTime", Sort.DESCENDING)
                    .limit(batchSize)
                    .find()
            }

            if (batch.isEmpty()) break

            var batchHasNewYear = false
            for (data in batch) {
                val currentYear = data.year

                if (years.add(currentYear)) {
                    batchHasNewYear = true
                    consecutiveSameYearCount = 0
                } else if (lastProcessedYear == currentYear) {
                    consecutiveSameYearCount++
                }

                lastProcessedYear = currentYear

                // 优化：如果连续处理了大量相同年份的数据，可能该年份数据很多
                // 可以跳过一些数据以提高性能
                if (consecutiveSameYearCount > 1000) {
                    break
                }
            }

            // 更新最后处理的时间戳
            if (batch.isNotEmpty()) {
                lastDataTime = batch.last().dataTime
            }

            // 如果这批数据没有新年份，且已有足够数据，可能已经处理完所有年份
            if (!batchHasNewYear && years.size > 5) break

            // 如果当前批次数据少于预期，说明已经到达末尾
            if (batch.size < batchSize) break
        }

        return years.toList().sortedDescending()
    }

    // 获取指定年份有数据的月份（带缓存）
    suspend fun getAvailableMonthsInYear(year: Int): List<Int> {
        return cacheManager.getOrCompute(
            key = DataCacheManager.Companion.KeyBuilder.yearMonths(year),
            ttl = DataCacheManager.Companion.CacheTTL.MEDIUM
        ) {
            getAvailableMonthsInYearFromDatabase(year)
        }
    }

    // 从数据库获取指定年份有数据月份的核心方法
    private suspend fun getAvailableMonthsInYearFromDatabase(year: Int): List<Int> {
        val yearStart = LocalDate(year, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val yearEnd = LocalDate(year + 1, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

        return realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime < $1", yearStart, yearEnd)
            .find()
            .map {
                Instant.fromEpochSeconds(it.dataTime)
                    .toLocalDateTime(TimeZone.currentSystemDefault())
                    .monthNumber
            }
            .distinct()
            .sorted()
            .reversed() // 最新月份在前
    }

    // 获取指定年月有数据的日期（带缓存）
    suspend fun getAvailableDaysInMonth(year: Int, month: Int): List<Int> {
        return cacheManager.getOrCompute(
            key = DataCacheManager.Companion.KeyBuilder.monthDays(year, month),
            ttl = DataCacheManager.Companion.CacheTTL.MEDIUM
        ) {
            getAvailableDaysInMonthFromDatabase(year, month)
        }
    }

    // 从数据库获取指定年月有数据日期的核心方法
    private suspend fun getAvailableDaysInMonthFromDatabase(year: Int, month: Int): List<Int> {
        val monthStart = LocalDate(year, month, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val monthEnd = if (month == 12) {
            LocalDate(year + 1, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        } else {
            LocalDate(year, month + 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        }

        return realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime < $1", monthStart, monthEnd)
            .find()
            .map {
                Instant.fromEpochSeconds(it.dataTime)
                    .toLocalDateTime(TimeZone.currentSystemDefault())
                    .dayOfMonth
            }
            .distinct()
            .sorted()
            .reversed() // 最新日期在前
    }

    // 获取指定日期的所有数据点，按时间排序（带缓存）
    suspend fun getDataPointsForDay(year: Int, month: Int, day: Int): List<StepDataRealmModel> = withContext(Dispatchers.IO) {
        cacheManager.getOrCompute(
            key = DataCacheManager.Companion.KeyBuilder.dayData(year, month, day),
            ttl = DataCacheManager.Companion.CacheTTL.MEDIUM
        ) {
            getDataPointsForDayFromDatabase(year, month, day)
        }
    }

    // 从数据库获取指定日期数据点的核心方法
    private suspend fun getDataPointsForDayFromDatabase(year: Int, month: Int, day: Int): List<StepDataRealmModel> {
        val dayStart = LocalDate(year, month, day).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val dayEnd = LocalDate(year, month, day).plus(DatePeriod(days = 1)).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

        return realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime < $1", dayStart, dayEnd)
            .sort("dataTime", Sort.ASCENDING)
            .find()
    }

    // 获取指定年份第一天有数据的日期
    suspend fun getFirstDataDayInYear(year: Int): Triple<Int, Int, Int>? {
        val yearStart = LocalDate(year, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val yearEnd = LocalDate(year + 1, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

        val firstData = realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime < $1", yearStart, yearEnd)
            .sort("dataTime", Sort.ASCENDING)
            .first()
            .find()

        return firstData?.let {
            val dateTime = Instant.fromEpochSeconds(it.dataTime)
                .toLocalDateTime(TimeZone.currentSystemDefault())
            Triple(dateTime.year, dateTime.monthNumber, dateTime.dayOfMonth)
        }
    }

    // 获取最后一天有数据的日期（带缓存）
    suspend fun getLastDataDay(): Triple<Int, Int, Int>? {
        return cacheManager.getOrCompute(
            key = DataCacheManager.Companion.CacheKeys.LAST_DATA_DAY,
            ttl = DataCacheManager.Companion.CacheTTL.SHORT
        ) {
            getLastDataDayFromDatabase()
        }
    }

    // 从数据库获取最后一天数据的核心方法
    private suspend fun getLastDataDayFromDatabase(): Triple<Int, Int, Int>? {
        val lastData = realm.query<StepDataRealmModel>()
            .sort("dataTime", Sort.DESCENDING)
            .first()
            .find()

        return lastData?.let {
            val dateTime = Instant.fromEpochSeconds(it.dataTime)
                .toLocalDateTime(TimeZone.currentSystemDefault())
            Triple(dateTime.year, dateTime.monthNumber, dateTime.dayOfMonth)
        }
    }

    // 获取指定日期之后的下一天有数据的日期
    suspend fun getNextDataDay(year: Int, month: Int, day: Int): Triple<Int, Int, Int>? {
        val currentDayEnd = LocalDate(year, month, day).plus(DatePeriod(days = 1)).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

        val nextData = realm.query<StepDataRealmModel>("dataTime >= $0", currentDayEnd)
            .sort("dataTime", Sort.ASCENDING)
            .first()
            .find()

        return nextData?.let {
            val dateTime = Instant.fromEpochSeconds(it.dataTime)
                .toLocalDateTime(TimeZone.currentSystemDefault())
            Triple(dateTime.year, dateTime.monthNumber, dateTime.dayOfMonth)
        }
    }

    // 获取指定年份所有有数据的日期（包含月份信息）（带缓存）
    suspend fun getAllDaysInYear(year: Int): List<Pair<Int, Int>> {
        return cacheManager.getOrCompute(
            key = DataCacheManager.Companion.KeyBuilder.yearDays(year),
            ttl = DataCacheManager.Companion.CacheTTL.MEDIUM
        ) {
            getAllDaysInYearFromDatabase(year)
        }
    }

    // 从数据库获取指定年份所有有数据的日期的核心方法
    private suspend fun getAllDaysInYearFromDatabase(year: Int): List<Pair<Int, Int>> {
        val yearStart = LocalDate(year, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val yearEnd = LocalDate(year + 1, 1, 1).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

        return realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime < $1", yearStart, yearEnd)
            .find()
            .map {
                val dateTime = Instant.fromEpochSeconds(it.dataTime)
                    .toLocalDateTime(TimeZone.currentSystemDefault())
                Pair(dateTime.monthNumber, dateTime.dayOfMonth)
            }
            .distinct()
            .sortedWith(compareByDescending<Pair<Int, Int>> { it.first }.thenByDescending { it.second })
    }

    // 获取指定日期的轨迹点数量（带缓存）
    suspend fun getDataPointCountForDay(year: Int, month: Int, day: Int): Int {
        return cacheManager.getOrCompute(
            key = DataCacheManager.Companion.KeyBuilder.dayCount(year, month, day),
            ttl = DataCacheManager.Companion.CacheTTL.LONG
        ) {
            getDataPointCountForDayFromDatabase(year, month, day)
        }
    }

    // 从数据库获取指定日期轨迹点数量的核心方法
    private suspend fun getDataPointCountForDayFromDatabase(year: Int, month: Int, day: Int): Int {
        val dayStart = LocalDate(year, month, day).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds
        val dayEnd = LocalDate(year, month, day).plus(DatePeriod(days = 1)).atStartOfDayIn(TimeZone.currentSystemDefault()).epochSeconds

        return realm.query<StepDataRealmModel>("dataTime >= $0 && dataTime < $1", dayStart, dayEnd)
            .count()
            .find()
            .toInt()
    }

    // ====== 删除数据方法 ======

    // 删除单个数据点
    suspend fun deleteDataPoint(dataTime: Long): Boolean {
        return try {
            realm.write {
                val dataPoint = query<StepDataRealmModel>("dataTime == $0", dataTime).first().find()
                dataPoint?.let { delete(it) }
            }
            // 删除成功后清除所有缓存
            clearAllCaches()
            true
        } catch (e: Exception) {
            println("Failed to delete data point: $dataTime, Error: ${e.message}")
            false
        }
    }

    // 批量删除数据点
    suspend fun deleteDataPoints(dataTimes: List<Long>): Boolean {
        return try {
            realm.write {
                dataTimes.forEach { dataTime ->
                    val dataPoint = query<StepDataRealmModel>("dataTime == $0", dataTime).first().find()
                    dataPoint?.let { delete(it) }
                }
            }
            // 删除成功后清除所有缓存
            clearAllCaches()
            true
        } catch (e: Exception) {
            println("Failed to delete data points: ${dataTimes.size} items, Error: ${e.message}")
            false
        }
    }

    // ====== 一生数据查询方法（按地图比例过滤） ======

    // 获取所有数据的总数量
    suspend fun getAllDataPointsCount(): Long {
        return realm.query<StepDataRealmModel>().count().find()
    }

    // 按地图视口和像素密度过滤数据点 - 优化版本（分页查询+流式处理）
    suspend fun getLifetimeDataPointsForViewportBatch(
        taskId: Int,
        minLat: Double,
        maxLat: Double,
        minLng: Double,
        maxLng: Double,
        screenWidthPx: Int,
        screenHeightPx: Int,
        onBatchReady: suspend (List<Point>) -> Unit,
        isCancelled: () -> Boolean = { false } // 取消检查函数
    ) = withContext(Dispatchers.IO) {
        // 计算每个像素对应的地理距离
        val latRange = maxLat - minLat
        val lngRange = maxLng - minLng
        val latPerPixel = latRange / screenHeightPx * 1.5
        val lngPerPixel = lngRange / screenWidthPx * 1.5

        val totalCount = realm.query<StepDataRealmModel>().count().find()

        // 使用网格过滤，确保每个像素最多一个点
        val gridMap = mutableMapOf<Pair<Int, Int>, StepDataRealmModel>()
        val batchSize = 1000

        val dbBatchSize = (totalCount / 4).toInt() // 数据库分页大小
        val returnedGridKeys = mutableSetOf<Pair<Int, Int>>()

        var totalProcessed = 0
        var hasMoreData = true

        println("lifetime: 开始优化查找数据 for viewport: lat[${minLat}, ${maxLat}], lng[${minLng}, ${maxLng}]")

        var lastDataTime = 0L // 用于基于时间戳的分页

        while (hasMoreData && !isCancelled()) {
            // 分页查询数据，避免一次性加载大量数据
            // 使用基于时间戳的分页，比offset更高效且正确
            val dataBatch = if (lastDataTime == 0L) {
                // 第一批数据，从最早的时间开始
                realm.query<StepDataRealmModel>(
                    "latitude >= $0 && latitude <= $1 && longitude >= $2 && longitude <= $3",
                    minLat, maxLat, minLng, maxLng
                )
                    .sort("dataTime", Sort.ASCENDING)
                    .limit(dbBatchSize)
                    .find()
            } else {
                // 后续批次，查询时间戳大于上一批最后一个数据的时间戳
                realm.query<StepDataRealmModel>(
                    "latitude >= $0 && latitude <= $1 && longitude >= $2 && longitude <= $3 && dataTime > $4",
                    minLat, maxLat, minLng, maxLng, lastDataTime
                )
                    .sort("dataTime", Sort.ASCENDING)
                    .limit(dbBatchSize)
                    .find()
            }

            if (dataBatch.isEmpty()) {
                hasMoreData = false
                break
            }

            println("lifetime: 处理数据批次 lastDataTime: $lastDataTime, size: ${dataBatch.size}, total processed: $totalProcessed")

            if (isCancelled()) {
                println("lifetime: 数据取消")
                return@withContext
            }

            // 处理当前批次的数据
            dataBatch.forEachIndexed { index, data ->
                // 每处理100条数据检查一次取消状态
                if (index % 10000 == 0) {
                    println("lifetime: 数据遍历中...")
                    if (isCancelled()) {
                        println("lifetime: 数据取消 at batch lastDataTime: $lastDataTime, index: $index")
                        return@withContext
                    }
                }

                // 计算数据点在屏幕网格中的位置
                val gridX = ((data.longitude - minLng) / lngPerPixel).toInt()
                val gridY = ((data.latitude - minLat) / latPerPixel).toInt()
                val gridKey = Pair(gridX, gridY)

                // 如果该网格位置还没有数据点，或者当前数据点时间更新，则使用当前数据点
                val existingData = gridMap[gridKey]
                if (existingData == null || data.dataTime > existingData.dataTime) {
                    gridMap[gridKey] = data
                }

                if (index % 10000 == 0) {
                    // 检查是否有足够的新网格数据可以返回一批
                    val availableGridKeys = gridMap.keys - returnedGridKeys
                    if (availableGridKeys.size >= batchSize) {
                        println("lifetime: 发送数据批次 ${availableGridKeys.size} grid points available, returning $batchSize")

                        // 取出前batchSize个未返回的网格点作为一批返回
                        val batchGridKeys = availableGridKeys.take(batchSize)
                        val batchPoints = batchGridKeys.map { gridKey ->
                            val data = gridMap[gridKey]!!
                            Point.fromLngLat(data.longitude, data.latitude)
                        }

                        // 标记这些网格键为已返回
                        returnedGridKeys.addAll(batchGridKeys)

                        // 回调返回这一批数据
                        onBatchReady(batchPoints)
                    }
                }

                totalProcessed++
            }

            if (isCancelled()) {
                println("lifetime: 数据取消")
                return@withContext
            }

            // 更新最后处理的时间戳
            if (dataBatch.isNotEmpty()) {
                lastDataTime = dataBatch.last().dataTime
            }

            // 检查是否有足够的新网格数据可以返回一批
            val availableGridKeys = gridMap.keys - returnedGridKeys
            if (availableGridKeys.size >= batchSize) {
                println("lifetime: 发送数据批次 ${availableGridKeys.size} grid points available, returning $batchSize")

                // 取出前batchSize个未返回的网格点作为一批返回
                val batchGridKeys = availableGridKeys.take(batchSize)
                val batchPoints = batchGridKeys.map { gridKey ->
                    val data = gridMap[gridKey]!!
                    Point.fromLngLat(data.longitude, data.latitude)
                }

                // 标记这些网格键为已返回
                returnedGridKeys.addAll(batchGridKeys)

                // 回调返回这一批数据
                onBatchReady(batchPoints)
            }

            // 如果当前批次数据少于预期，说明已经到达末尾
            if (dataBatch.size < dbBatchSize) {
                hasMoreData = false
            }

            if (isCancelled()) {
                println("lifetime: 数据取消")
                return@withContext
            }

            // 给其他协程一些执行时间，避免阻塞UI
            kotlinx.coroutines.yield()
        }

        // 最后检查是否已被取消
        if (isCancelled()) {
            println("lifetime: 数据取消 cancelled before final batch")
            return@withContext
        }

        // 处理剩余的未返回数据
        val remainingGridKeys = gridMap.keys - returnedGridKeys
        if (remainingGridKeys.isNotEmpty()) {
            println("lifetime: 发送最终批次 ${remainingGridKeys.size} remaining points")
            val remainingPoints = remainingGridKeys.map { gridKey ->
                val data = gridMap[gridKey]!!
                Point.fromLngLat(data.longitude, data.latitude)
            }
            onBatchReady(remainingPoints)
        }

        println("lifetime: 数据处理完成 total processed: $totalProcessed, final grid points: ${gridMap.size}")
    }

    // 保留原有方法以兼容现有代码
    suspend fun getLifetimeDataPointsForViewport(
        taskId: Int,
        minLat: Double,
        maxLat: Double,
        minLng: Double,
        maxLng: Double,
        screenWidthPx: Int,
        screenHeightPx: Int,
        isCancelled: () -> Boolean = { false }
    ): List<Point> = withContext(Dispatchers.IO) {
        val allPoints = mutableListOf<Point>()
        getLifetimeDataPointsForViewportBatch(
            taskId,
            minLat, maxLat, minLng, maxLng, screenWidthPx, screenHeightPx,
            onBatchReady = { batch ->
                allPoints.addAll(batch)
            },
            isCancelled = isCancelled
        )
        allPoints
    }


    // 获取全部数据的边界框，用于初始化地图视口
    suspend fun getAllDataBounds(): Pair<Pair<Double, Double>, Pair<Double, Double>>? = withContext(Dispatchers.IO) {
        val minLatResult = realm.query<StepDataRealmModel>().sort("latitude", Sort.ASCENDING).first().find()
        val maxLatResult = realm.query<StepDataRealmModel>().sort("latitude", Sort.DESCENDING).first().find()
        val minLngResult = realm.query<StepDataRealmModel>().sort("longitude", Sort.ASCENDING).first().find()
        val maxLngResult = realm.query<StepDataRealmModel>().sort("longitude", Sort.DESCENDING).first().find()

        if (minLatResult == null || maxLatResult == null || minLngResult == null || maxLngResult == null) {
            return@withContext null
        }

        Pair(
            Pair(minLatResult.latitude, maxLatResult.latitude),
            Pair(minLngResult.longitude, maxLngResult.longitude)
        )
    }


    // 获取一生数据的统计信息（两阶段缓存策略）
    suspend fun getLifetimeDataStats(onDataUpdated: ((Triple<Long, Double, Long>) -> Unit)? = null): Triple<Long, Double, Long> {
        // 第一阶段：立即从 AppPrefs 返回缓存数据
        val cachedTrackPoints = AppPrefs.sharedInstance.dataCacheTotalTrackPoints
        val cachedDistance = AppPrefs.sharedInstance.dataCacheTotalDistance
        val cachedDays = AppPrefs.sharedInstance.dataCacheTotalDays
        val cachedResult = Triple(cachedTrackPoints, cachedDistance, cachedDays)

        // 第二阶段：启动异步协程从数据库获取最新数据并更新缓存
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val freshResult = getLifetimeDataStatsFromDatabase()

                // 只有当数据发生变化时才更新 AppPrefs 和回调
                if (freshResult != cachedResult) {
                    // 更新 AppPrefs 缓存
                    AppPrefs.sharedInstance.dataCacheTotalTrackPoints = freshResult.first
                    AppPrefs.sharedInstance.dataCacheTotalDistance = freshResult.second
                    AppPrefs.sharedInstance.dataCacheTotalDays = freshResult.third

                    println("LifetimeStats: Updated AppPrefs cache - TrackPoints: ${freshResult.first}, Distance: ${freshResult.second}, Days: ${freshResult.third}")

                    // 在主线程中通知调用者数据已更新
                    withContext(Dispatchers.Main) {
                        onDataUpdated?.invoke(freshResult)
                    }
                }
            } catch (e: Exception) {
                println("LifetimeStats: Failed to update cache from database: ${e.message}")
            }
        }

        // 立即返回缓存的数据
        return cachedResult
    }

    // 兼容方法：不带回调的版本，用于现有代码
    suspend fun getLifetimeDataStatsCompat(): Triple<Long, Double, Long> {
        return getLifetimeDataStats(null)
    }

    // 从数据库获取一生数据统计信息的核心方法
    private suspend fun getLifetimeDataStatsFromDatabase(): Triple<Long, Double, Long> {

        try {
            // 使用Realm的聚合查询直接计算，避免逐条遍历
            val totalCount = realm.query<StepDataRealmModel>().count().find()

            if (totalCount <= 1) {
                return Triple(totalCount, 0.0, 0L)
            }

            // 使用Realm的sum聚合函数直接计算总距离，性能远超逐条累加
            // 可以添加查询条件来过滤数据，例如：
             val totalDistance = realm.query<StepDataRealmModel>("distance < 10000").sum("distance", Double::class).find()
//            val totalDistance = realm.query<StepDataRealmModel>().sum("distance", Double::class).find()

            // 获取第一个和最后一个数据点的时间戳来计算累计时长
            val firstData = realm.query<StepDataRealmModel>()
                .sort("dataTime", Sort.ASCENDING)
                .first()
                .find()

            val lastData = realm.query<StepDataRealmModel>()
                .sort("dataTime", Sort.DESCENDING)
                .first()
                .find()

            val totalDays = if (firstData != null && lastData != null) {
                val firstDateTime = Instant.fromEpochSeconds(firstData.dataTime)
                    .toLocalDateTime(TimeZone.currentSystemDefault()).date
                val lastDateTime = Instant.fromEpochSeconds(lastData.dataTime)
                    .toLocalDateTime(TimeZone.currentSystemDefault()).date

                // 计算天数差（包含首尾两天）
                val daysDiff = lastDateTime.toEpochDays() - firstDateTime.toEpochDays() + 1
                daysDiff.toLong()
            } else {
                0L
            }

            val result = Triple(totalCount, totalDistance, totalDays)

            println("Lifetime stats calculation completed (optimized): $totalCount points, ${totalDistance}km total distance, ${totalDays} days")

            return result

        } catch (e: Exception) {
            println("Error in optimized getLifetimeDataStats: ${e.message}")
            // 如果聚合查询失败，回退到分批处理方式
            return getLifetimeDataStatsLegacy()
        }
    }

    // 保留原有的分批处理方法作为备用方案
    private suspend fun getLifetimeDataStatsLegacy(): Triple<Long, Double, Long> = withContext(Dispatchers.IO) {
        // 先获取总数量
        val totalCount = realm.query<StepDataRealmModel>().count().find()

        if (totalCount <= 1) {
            return@withContext Triple(totalCount, 0.0, 0L)
        }

        // 分批计算总距离，避免内存暴增
        var totalDistance = 0.0
        val batchSize = 20000 // 增大批次大小以提高性能
        var processedCount = 0L
        var lastDataTime = 0L // 用于基于时间戳的分页
        var firstDataTime: Long? = null
        var lastDataTimeFound: Long? = null

        println("Using legacy batch processing for lifetime stats calculation")

        while (processedCount < totalCount) {
            try {
                // 使用基于时间戳的分页查询，比offset更高效
                val batch = if (lastDataTime == 0L) {
                    // 第一批数据，从最早的时间开始
                    realm.query<StepDataRealmModel>()
                        .sort("dataTime", Sort.ASCENDING)
                        .limit(batchSize)
                        .find()
                } else {
                    // 后续批次，查询时间戳大于上一批最后一个数据的时间戳
                    realm.query<StepDataRealmModel>("dataTime > $0", lastDataTime)
                        .sort("dataTime", Sort.ASCENDING)
                        .limit(batchSize)
                        .find()
                }

                // 安全地处理批次数据
                val batchCount = batch.size
                if (batchCount == 0) break

                println("Processing legacy batch: ${processedCount + 1}-${processedCount + batchCount} of $totalCount")

                // 使用批量累加而不是逐条处理，提高性能
                val batchDistance = batch.sumOf { it.distance }
                totalDistance += batchDistance

                // 记录第一个和最后一个数据点的时间戳
                if (firstDataTime == null && batch.isNotEmpty()) {
                    firstDataTime = batch.first().dataTime
                }
                if (batch.isNotEmpty()) {
                    lastDataTimeFound = batch.last().dataTime
                }

                // 更新最后处理的时间戳
                if (batchCount > 0) {
                    lastDataTime = batch.last().dataTime
                }

                processedCount += batchCount

                // 如果处理的数据少于批次大小，说明已经到达末尾
                if (batchCount < batchSize) break

                // 给其他协程一些执行时间，避免阻塞UI
                kotlinx.coroutines.yield()

            } catch (e: Exception) {
                println("Error processing legacy batch at processed count $processedCount: ${e.message}")
                break
            }
        }

        // 计算累计时长
        val totalDays = if (firstDataTime != null && lastDataTimeFound != null) {
            val firstDateTime = Instant.fromEpochSeconds(firstDataTime)
                .toLocalDateTime(TimeZone.currentSystemDefault()).date
            val lastDateTime = Instant.fromEpochSeconds(lastDataTimeFound)
                .toLocalDateTime(TimeZone.currentSystemDefault()).date

            // 计算天数差（包含首尾两天）
            val daysDiff = lastDateTime.toEpochDays() - firstDateTime.toEpochDays() + 1
            daysDiff.toLong()
        } else {
            0L
        }

        println("Legacy lifetime stats calculation completed: $totalCount points, ${totalDistance}km total distance, ${totalDays} days")

        Triple(totalCount, totalDistance, totalDays)
    }

    // 清除统计信息缓存（当有新数据写入时调用）
    suspend fun clearStatsCache() {
        cacheManager.remove(DataCacheManager.Companion.CacheKeys.LIFETIME_STATS)

        // 同时清除 AppPrefs 中的缓存，强制下次重新计算
        AppPrefs.sharedInstance.dataCacheTotalTrackPoints = 0L
        AppPrefs.sharedInstance.dataCacheTotalDistance = 0.0
        AppPrefs.sharedInstance.dataCacheTotalDays = 0L

        println("Lifetime stats cache cleared (both DataCacheManager and AppPrefs)")
    }

    // 清除年份缓存
    suspend fun clearYearsCache() {
        cacheManager.remove(DataCacheManager.Companion.CacheKeys.AVAILABLE_YEARS)
        println("Years cache cleared")
    }

    // 清除最后一天缓存
    suspend fun clearLastDayCache() {
        cacheManager.remove(DataCacheManager.Companion.CacheKeys.LAST_DATA_DAY)
        println("Last day cache cleared")
    }

    // 清除指定年份的相关缓存
    suspend fun clearYearRelatedCache(year: Int) {
        cacheManager.remove(DataCacheManager.Companion.KeyBuilder.yearDays(year))
        cacheManager.remove(DataCacheManager.Companion.KeyBuilder.yearMonths(year))
        cacheManager.remove(DataCacheManager.Companion.KeyBuilder.firstDayYear(year))
        cacheManager.removeByPrefix("${DataCacheManager.Companion.CacheKeys.DAY_DATA_PREFIX}${year}_")
        cacheManager.removeByPrefix("${DataCacheManager.Companion.CacheKeys.DAY_COUNT_PREFIX}${year}_")
        cacheManager.removeByPrefix("${DataCacheManager.Companion.CacheKeys.MONTH_DAYS_PREFIX}${year}_")
        cacheManager.removeByPrefix("${DataCacheManager.Companion.CacheKeys.NEXT_DAY_PREFIX}${year}_")
        println("Cleared cache for year $year")
    }

    // 清除所有缓存
    suspend fun clearAllCaches() {
        cacheManager.clearAll()
        println("All caches cleared")
    }

    // 当数据发生变化时，智能清理相关缓存
    suspend fun onDataChanged(year: Int? = null, month: Int? = null, day: Int? = null) {
        // 清除统计信息缓存（因为数据总量可能变化）
        clearStatsCache()

        // 清除年份列表缓存（可能有新年份）
        clearYearsCache()

        // 清除最后一天缓存（最后一天可能变化）
        clearLastDayCache()

        // 如果指定了具体日期，清除相关缓存
        if (year != null) {
            clearYearRelatedCache(year)

            if (month != null && day != null) {
                // 清除具体日期的缓存
                cacheManager.remove(DataCacheManager.Companion.KeyBuilder.dayData(year, month, day))
                cacheManager.remove(DataCacheManager.Companion.KeyBuilder.dayCount(year, month, day))
            }
        }

        println("Smart cache cleanup completed for data change")
    }

}

// 将扩展函数移到类外部
fun List<StepDataRealmModel>.convertStepDataToPoints(): List<Point> {
    return try {
        this.mapNotNull { stepData ->
            try {
                Point.fromLngLat(stepData.longitude, stepData.latitude)
            } catch (e: Exception) {
                println("Error converting data point to Point: ${e.message}")
                null
            }
        }
    } catch (e: Exception) {
        println("Error in convertStepDataToPoints: ${e.message}")
        emptyList()
    }
}
