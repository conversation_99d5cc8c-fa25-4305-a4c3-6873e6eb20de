package com.lfb.android.footprint

import android.app.Application
import android.content.Intent
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.lfb.android.footprint.location.LocationDataRecorder
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.ui.theme.MapThemeManager
import io.realm.kotlin.Realm
import io.realm.kotlin.RealmConfiguration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import android.app.ActivityManager
import android.content.Context
import com.lfb.android.footprint.Manager.RealmModelManager
import com.lfb.android.footprint.utils.ScreenManager

class MyApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        AppPrefs.init(this)

        // 初始化主题管理器，确保主题状态与用户配置同步
        MapThemeManager.updateMapDisplayType(AppPrefs.sharedInstance.mapDisplayType)

        // Realm 初始化（传递 Context 以支持磁盘缓存）
        RealmModelManager.getInstance(this)

        // 应用启动时执行数据迁移
//        migrateLocationData()

        ProcessLifecycleOwner.get().lifecycle.addObserver(AppLifecycleObserver(this))
    }

    companion object {
        @Volatile
        private var instance: MyApplication? = null

        fun getInstance(): MyApplication {
            return instance ?: synchronized(this) {
                instance ?: throw IllegalStateException("MyApplication not initialized")
            }
        }
    }
}

/**
 * 应用状态管理器
 * 用于跟踪应用的前台后台状态
 */
object AppStateManager {
    @Volatile
    private var isAppInForeground = false

    fun setAppInForeground(inForeground: Boolean) {
        isAppInForeground = inForeground
    }

    fun isAppInForeground(): Boolean {
        return isAppInForeground
    }
}

class AppLifecycleObserver(private val app: Application) : LifecycleEventObserver {
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when (event) {
            Lifecycle.Event.ON_START -> {
                // 更新应用前台状态
                AppStateManager.setAppInForeground(true)

                // 检查当前是否有Activity在前台，避免干扰其他Activity
                if (!isAnyActivityInForeground()) {
                    // App进入前台，跳转MainActivity（可根据条件修改）
                    val intent = Intent(app, MainMapActivity::class.java).apply {
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    app.startActivity(intent)
                }

                LocalLogManager.getInstance(app).logSync("app 进入前台")

                // 执行数据迁移
                migrateLocationData()
            }
            Lifecycle.Event.ON_STOP -> {
                // 更新应用后台状态
                AppStateManager.setAppInForeground(false)

                // App进入后台，释放屏幕常亮资源以节省电量
                ScreenManager.release()
                LocalLogManager.getInstance(app).logSync("app 进入后台，已释放屏幕常亮资源")
            }
            else -> {}
        }
    }

    private fun isAnyActivityInForeground(): Boolean {
        val activityManager = app.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = try {
            activityManager.getRunningTasks(1)
        } catch (e: Exception) {
            return false
        }

        return runningTasks.isNotEmpty() &&
               runningTasks[0].topActivity?.packageName == app.packageName
    }

    fun migrateLocationData() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                LocationDataRecorder.getInstance(app).saveAllLocation()
            } catch (e: Exception) {
                android.util.Log.e("MyApplication", "Error saving location data: ${e.message}")
            }
        }
    }
}