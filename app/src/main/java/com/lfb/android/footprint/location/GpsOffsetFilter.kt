package com.lfb.android.footprint.location

import android.location.Location
import android.content.Context
import com.lfb.android.footprint.prefs.AppPrefs
import com.lfb.android.footprint.Manager.RealmModelManager
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * GPS偏移过滤器
 * 用于检测和过滤GPS轨迹中的偏移数据点（偏移出去又回来的异常轨迹）
 *
 * 核心算法原理：
 * 检测"突兀"的轨迹点 - 即相对于前后轨迹都显得不连续的点
 * 1. 维护一个位置缓冲区，等待足够的前后轨迹数据
 * 2. 当有足够数据时，检查中间的点是否相对于前后轨迹都偏离较远
 * 3. 如果一个点到前面轨迹和后面轨迹的距离都很大，则认为是偏移点
 * 4. 使用延迟判断机制，确保有足够的后续数据来验证
 */
class GpsOffsetFilter(private val context: Context) {
    
    companion object {
        private const val TAG = "GpsOffsetFilter"

        // 轨迹缓冲区大小 - 需要足够的前后数据来判断突兀性
        private const val TRACK_BUFFER_SIZE = 7

        // 延迟判断的缓冲区大小 - 等待后续数据
        private const val PENDING_BUFFER_SIZE = 3

        // 突兀点检测的角度阈值（度）- 前后轨迹方向差异超过此角度认为突兀
        private const val OUTLIER_ANGLE_THRESHOLD = 120.0

        // 最小距离阈值（米）- 距离太近时角度计算不准确
        private const val MIN_DISTANCE_FOR_ANGLE = 15.0

        // GPS精度阈值（米）- accuracy值大于此阈值表示精度差，更容易被认为是偏移
        // 注意：accuracy值越小表示精度越高，值越大表示精度越低
        private const val LOW_ACCURACY_THRESHOLD = 30.0

        // 极差精度阈值（米）- 精度非常差的点，更严格过滤
        private const val VERY_LOW_ACCURACY_THRESHOLD = 100.0

        // 时间间隔阈值（秒）- 超过此时间间隔的点需要特殊处理
        private const val LARGE_TIME_GAP_THRESHOLD = 300 // 5分钟

        // 大距离跳跃阈值（米）- 超过此距离的跳跃需要特殊检查
        private const val LARGE_DISTANCE_JUMP_THRESHOLD = 200.0
    }
    
    // 轨迹缓冲区（存储所有接收到的位置点，用于前后轨迹分析）
    private val trackBuffer = mutableListOf<Location>()

    // 待处理的位置缓冲区（等待后续数据来判断的点）
    private val pendingBuffer = mutableListOf<Location>()

    // 已确认接受的位置点列表
    private val acceptedLocations = mutableListOf<Location>()

    // 是否已经初始化历史数据
    private var isHistoryLoaded = false

    // 记录当前数据所属的日期，用于跨天检测
    private var currentDataDate: String? = null
    
    /**
     * 判断是否应该接受这个位置点
     * @param location 新的位置点
     * @return true表示接受，false表示过滤掉
     */
    fun shouldAcceptLocation(location: Location): Boolean {
        // 检查是否跨天，如果跨天则重置
        checkAndHandleDayChange(location)

        // 首次使用时或跨天后加载当日历史数据
        if (!isHistoryLoaded) {
            loadTodayHistoryData()
            isHistoryLoaded = true
        }

        // 检查是否是长时间间隔后的第一个点，需要特殊处理
        if (trackBuffer.isNotEmpty()) {
            val lastPoint = trackBuffer.last()
            val timeGap = (location.time - lastPoint.time) / 1000 // 秒
            if (timeGap > LARGE_TIME_GAP_THRESHOLD) {
                // 长时间间隔后的点，如果精度很差且距离跳跃很大，可能是偏移
                val distance = lastPoint.distanceTo(location)
                if (location.accuracy > VERY_LOW_ACCURACY_THRESHOLD && distance > LARGE_DISTANCE_JUMP_THRESHOLD) {
                    LocalLogManager.getInstance(context).logSync(
                        "GPS偏移过滤器过滤长时间间隔后的低精度大跳跃点: " +
                        "lat=${location.latitude}, lng=${location.longitude}, " +
                        "accuracy=${location.accuracy}m, timeGap=${timeGap}s, distance=${distance}m"
                    )
                    return false
                }
            }
        }

        // 将新位置添加到轨迹缓冲区
        addToTrackBuffer(location)

        // 如果历史数据不足，直接接受
        if (trackBuffer.size <= 3) {
            acceptedLocations.add(location)
            return true
        }

        // 处理待处理缓冲区中的点
        processPendingLocations()

        // 将当前点添加到待处理缓冲区
        pendingBuffer.add(location)

        // 如果待处理缓冲区满了，强制处理最早的点
        if (pendingBuffer.size > PENDING_BUFFER_SIZE) {
            val locationToProcess = pendingBuffer.removeAt(0)
            val shouldAccept = !isOutlierLocation(locationToProcess)
            if (shouldAccept) {
                acceptedLocations.add(locationToProcess)
            }
            return shouldAccept
        }

        // 暂时不返回结果，等待更多数据
        return true // 临时返回true，实际的过滤在processPendingLocations中进行
    }
    
    /**
     * 处理待处理缓冲区中的位置点
     */
    private fun processPendingLocations() {
        // 如果轨迹数据不足，无法判断
        if (trackBuffer.size < 5) return

        val locationsToProcess = mutableListOf<Location>()

        // 检查待处理缓冲区中的每个点
        for (i in pendingBuffer.indices) {
            val location = pendingBuffer[i]
            if (canProcessLocation(location)) {
                locationsToProcess.add(location)
            }
        }

        // 处理可以判断的位置点
        for (location in locationsToProcess) {
            pendingBuffer.remove(location)
            val shouldAccept = !isOutlierLocation(location)
            if (shouldAccept) {
                acceptedLocations.add(location)
            }
        }
    }

    /**
     * 检查是否可以处理某个位置点（是否有足够的前后数据）
     */
    private fun canProcessLocation(location: Location): Boolean {
        val locationIndex = trackBuffer.indexOf(location)
        if (locationIndex == -1) return false

        // 需要前面至少有2个点，后面至少有2个点
        return locationIndex >= 2 && locationIndex < trackBuffer.size - 2
    }

    /**
     * 检查位置是否为突兀的偏移点
     * 核心逻辑：检查该点是否破坏了轨迹的自然连续性
     *
     * 关键思路：
     * - 真正的偏移：该点偏离了前后两点的合理连接路径
     * - 正常转弯/调头：虽然角度变化大，但轨迹仍然连续合理
     * - 判断标准：点到直线的偏离距离 + 路径合理性 + 时间间隔 + 精度
     */
    private fun isOutlierLocation(location: Location): Boolean {
        val locationIndex = trackBuffer.indexOf(location)
        if (locationIndex == -1 || locationIndex < 1 || locationIndex >= trackBuffer.size - 1) {
            return false
        }

        val beforePoint = trackBuffer[locationIndex - 1]
        val afterPoint = trackBuffer[locationIndex + 1]

        // 检查时间间隔 - 如果时间间隔很大，需要更谨慎的判断
        val timeBefore = (location.time - beforePoint.time) / 1000 // 秒
        val timeAfter = (afterPoint.time - location.time) / 1000 // 秒
        val hasLargeTimeGap = timeBefore > LARGE_TIME_GAP_THRESHOLD || timeAfter > LARGE_TIME_GAP_THRESHOLD

        // 检查距离跳跃
        val distanceBefore = beforePoint.distanceTo(location)
        val distanceAfter = location.distanceTo(afterPoint)
        val directDistance = beforePoint.distanceTo(afterPoint)

        val hasLargeDistanceJump = distanceBefore > LARGE_DISTANCE_JUMP_THRESHOLD ||
                                  distanceAfter > LARGE_DISTANCE_JUMP_THRESHOLD

        // 如果距离太近，无法准确判断轨迹偏离
        if (distanceBefore < MIN_DISTANCE_FOR_ANGLE ||
            distanceAfter < MIN_DISTANCE_FOR_ANGLE ||
            directDistance < MIN_DISTANCE_FOR_ANGLE) {

            // 但如果精度很差且有大距离跳跃，仍然可能是偏移
            if (location.accuracy > VERY_LOW_ACCURACY_THRESHOLD && hasLargeDistanceJump) {
                LocalLogManager.getInstance(context).logSync("GPS偏移过滤器过滤低精度大跳跃点: lat=${location.latitude}, lng=${location.longitude}, accuracy=${location.accuracy}, distBefore=${distanceBefore}m, distAfter=${distanceAfter}m")
                return true
            }
            return false
        }

        // 核心判断：检查该点是否偏离了前后两点的合理路径
        val isOutlier = checkTrajectoryDeviation(beforePoint, location, afterPoint, hasLargeTimeGap, hasLargeDistanceJump)

        // 记录过滤结果
        if (isOutlier) {
            LocalLogManager.getInstance(context).logSync("GPS偏移过滤器过滤偏移点: lat=${location.latitude}, lng=${location.longitude}, accuracy=${location.accuracy}, timeBefore=${timeBefore}s, timeAfter=${timeAfter}s")
        }

        return isOutlier
    }

    /**
     * 检查轨迹偏离度
     * 判断中间点是否偏离了前后两点的合理连接路径
     *
     * 这种方法可以区分：
     * - 偏移：点偏离直线很远，且绕行距离不合理
     * - 转弯：虽然不在直线上，但绕行距离合理
     * - 调头：绕行距离较长，但仍在合理范围内
     */
    private fun checkTrajectoryDeviation(
        beforePoint: Location,
        middlePoint: Location,
        afterPoint: Location,
        hasLargeTimeGap: Boolean = false,
        hasLargeDistanceJump: Boolean = false
    ): Boolean {
        // 计算点到直线的距离（中间点到前后两点连线的距离）
        val deviationDistance = calculatePointToLineDistance(beforePoint, afterPoint, middlePoint)

        // 计算路径长度比 = (前→中→后的距离) / (前→后的直线距离)
        val actualPath = beforePoint.distanceTo(middlePoint) + middlePoint.distanceTo(afterPoint)
        val directPath = beforePoint.distanceTo(afterPoint)
        val pathRatio = if (directPath > 0) actualPath / directPath else 1.0

        // 根据精度调整敏感度
        val sensitivity = when {
            middlePoint.accuracy > VERY_LOW_ACCURACY_THRESHOLD -> 0.6 // 极差精度，更严格
            middlePoint.accuracy > LOW_ACCURACY_THRESHOLD -> 0.8      // 差精度，较严格
            else -> 1.2                                               // 好精度，较宽松
        }

        // 基础偏离距离阈值
        var baseDeviationThreshold = when {
            middlePoint.accuracy > VERY_LOW_ACCURACY_THRESHOLD -> 50.0  // 极差精度点，更严格
            middlePoint.accuracy > LOW_ACCURACY_THRESHOLD -> 60.0       // 差精度点，较严格
            else -> 80.0                                                // 好精度点，较宽松
        }

        // 如果有大时间间隔或大距离跳跃，降低阈值（更严格）
        if (hasLargeTimeGap || hasLargeDistanceJump) {
            baseDeviationThreshold *= 0.7
        }

        val deviationThreshold = baseDeviationThreshold / sensitivity

        // 路径比例阈值 - 根据时间间隔和距离跳跃调整
        var basePathRatioThreshold = when {
            hasLargeTimeGap && hasLargeDistanceJump -> 2.0  // 时间间隔大且距离跳跃大，更严格
            hasLargeTimeGap || hasLargeDistanceJump -> 2.5  // 其中一个条件满足，较严格
            else -> 3.0                                     // 正常情况
        }

        val pathRatioThreshold = basePathRatioThreshold / sensitivity

        // 额外检查：如果精度很差且偏离距离很大，直接认为是偏移
        if (middlePoint.accuracy > VERY_LOW_ACCURACY_THRESHOLD && deviationDistance > 100.0) {
            return true
        }

        // 同时满足两个条件才认为是偏移：
        // 1. 偏离直线距离过大
        // 2. 绕行距离过长（超出正常转弯/调头范围）
        val isDeviation = deviationDistance > deviationThreshold
        val isLongPath = pathRatio.toDouble() > pathRatioThreshold

        // 记录详细的判断信息用于调试
        if (isDeviation || isLongPath) {
            LocalLogManager.getInstance(context).logSync(
                "GPS偏移分析: deviation=${deviationDistance.toInt()}m(阈值${deviationThreshold.toInt()}), " +
                "pathRatio=${String.format("%.2f", pathRatio)}(阈值${String.format("%.2f", pathRatioThreshold)}), " +
                "accuracy=${middlePoint.accuracy}m, timeGap=$hasLargeTimeGap, distJump=$hasLargeDistanceJump, " +
                "结果=${isDeviation && isLongPath}"
            )
        }

        return isDeviation && isLongPath
    }
    
    /**
     * 计算点到直线的距离
     * 使用向量叉积计算点到直线的垂直距离
     */
    private fun calculatePointToLineDistance(lineStart: Location, lineEnd: Location, point: Location): Double {
        // 将经纬度转换为平面坐标（简化计算，适用于小范围）
        val x1 = lineStart.longitude
        val y1 = lineStart.latitude
        val x2 = lineEnd.longitude
        val y2 = lineEnd.latitude
        val x0 = point.longitude
        val y0 = point.latitude

        // 计算直线的长度
        val lineLength = sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1))

        if (lineLength == 0.0) {
            // 如果直线长度为0，返回点到点的距离
            return lineStart.distanceTo(point).toDouble()
        }

        // 使用点到直线距离公式：|ax0 + by0 + c| / sqrt(a² + b²)
        // 直线方程：(y2-y1)x - (x2-x1)y + (x2-x1)y1 - (y2-y1)x1 = 0
        val a = y2 - y1
        val b = x1 - x2
        val c = (x2 - x1) * y1 - (y2 - y1) * x1

        val distance = abs(a * x0 + b * y0 + c) / sqrt(a * a + b * b)

        // 将度数差转换为大概的米数（1度约等于111000米）
        return distance * 111000.0
    }

    /**
     * 计算两个角度之间的差异（0-180度）
     */
    private fun calculateAngleDifference(angle1: Float, angle2: Float): Double {
        var diff = abs(angle1 - angle2).toDouble()
        if (diff > 180) {
            diff = 360 - diff
        }
        return diff
    }


    
    /**
     * 添加位置到轨迹缓冲区
     */
    private fun addToTrackBuffer(location: Location) {
        trackBuffer.add(location)
        if (trackBuffer.size > TRACK_BUFFER_SIZE) {
            trackBuffer.removeAt(0)
        }
    }

    /**
     * 重置过滤器状态
     */
    private fun resetFilter() {
        trackBuffer.clear()
        pendingBuffer.clear()
        acceptedLocations.clear()
        isHistoryLoaded = false
        currentDataDate = null
    }

    /**
     * 获取当前过滤器状态信息（用于调试）
     */
    fun getFilterStatus(): String {
        return "Track: ${trackBuffer.size}, Pending: ${pendingBuffer.size}, Accepted: ${acceptedLocations.size}"
    }

    /**
     * 检查并处理跨天情况
     * 如果检测到跨天，重置所有缓冲区并重新加载历史数据
     */
    private fun checkAndHandleDayChange(location: Location) {
        // 获取当前GPS点的日期
        val locationDate = getDateString(location.time)

        // 如果是第一次或者日期发生变化
        if (currentDataDate == null) {
            currentDataDate = locationDate
        } else if (currentDataDate != locationDate) {
            // 检测到跨天，重置所有状态
            LocalLogManager.getInstance(context).logSync("GPS偏移过滤器检测到跨天：$currentDataDate → $locationDate，重置过滤器状态")

            trackBuffer.clear()
            pendingBuffer.clear()
            acceptedLocations.clear()
            isHistoryLoaded = false
            currentDataDate = locationDate
        }
    }

    /**
     * 将时间戳转换为日期字符串（YYYY-MM-DD格式）
     */
    private fun getDateString(timestamp: Long): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        return String.format(
            "%04d-%02d-%02d",
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH)
        )
    }

    /**
     * 加载当日历史数据到轨迹缓冲区
     * 为过滤器提供轨迹上下文，避免APP重启后失去历史轨迹信息
     */
    private fun loadTodayHistoryData() {
        try {
            // 获取今天的日期
            val today = Calendar.getInstance()
            val year = today.get(Calendar.YEAR)
            val month = today.get(Calendar.MONTH) + 1 // Calendar.MONTH 是0-based
            val day = today.get(Calendar.DAY_OF_MONTH)

            // 使用协程在后台线程加载数据
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                try {
                    // 从数据库获取当日的GPS数据
                    val todayData = RealmModelManager.getInstance().getDataPointsForDay(year, month, day)

                    // 按时间排序（数据库查询已经排序，但为了确保）
                    val sortedData = todayData.sortedBy { it.dataTime }

                    // 只取最近的数据点，避免缓冲区过大
                    val recentData = if (sortedData.size > TRACK_BUFFER_SIZE) {
                        sortedData.takeLast(TRACK_BUFFER_SIZE)
                    } else {
                        sortedData
                    }

                    // 在主线程更新缓冲区
                    kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                        // 转换为Location对象并添加到轨迹缓冲区
                        for (stepData in recentData) {
                            val location = Location("").apply {
                                latitude = stepData.latitude
                                longitude = stepData.longitude
                                time = stepData.dataTime * 1000 // 转换为毫秒
                                accuracy = stepData.hAccuracy.toFloat()
                                if (stepData.speed > 0) {
                                    speed = stepData.speed.toFloat()
                                }
                                if (stepData.altitude != 0.0) {
                                    altitude = stepData.altitude
                                }
                            }
                            trackBuffer.add(location)
                        }

                        // 记录加载的数据量
                        if (trackBuffer.isNotEmpty()) {
                            LocalLogManager.getInstance(context).logSync("GPS偏移过滤器已加载当日历史数据: ${trackBuffer.size} 个点")
                        }
                    }

                } catch (e: Exception) {
                    LocalLogManager.getInstance(context).logSync("GPS偏移过滤器加载当日历史数据失败: ${e.message}")
                }
            }

        } catch (e: Exception) {
            LocalLogManager.getInstance(context).logSync("GPS偏移过滤器启动历史数据加载失败: ${e.message}")
        }
    }

    /**
     * 强制处理所有待处理的位置点（在停止定位时调用）
     */
    fun flushPendingLocations(): List<Location> {
        val result = mutableListOf<Location>()

        // 处理所有待处理的点
        for (location in pendingBuffer) {
            val shouldAccept = !isOutlierLocation(location)
            if (shouldAccept) {
                acceptedLocations.add(location)
                result.add(location)
            }
        }

        pendingBuffer.clear()
        return result
    }

    /**
     * 手动重新加载历史数据（当需要刷新上下文时调用）
     */
    fun reloadHistoryData() {
        trackBuffer.clear()
        pendingBuffer.clear()
        acceptedLocations.clear()
        isHistoryLoaded = false
        currentDataDate = null
    }

    /**
     * 获取当前过滤器的日期状态（用于调试）
     */
    fun getCurrentDate(): String? {
        return currentDataDate
    }
}
